export type Updater<T> = (updater: (value: T) => void) => void;

export const ROLES = ["system", "user", "assistant"] as const;
export type MessageRole = (typeof ROLES)[number];

export interface RequestMessage {
  role: MessageRole;
  content: string;
}

export type DalleSize = "1024x1024" | "1792x1024" | "1024x1792";
export type DalleQuality = "standard" | "hd";
export type DalleStyle = "vivid" | "natural";

// 多提供商相关类型
export interface ProviderValidationResult {
  isValid: boolean;
  missingFields: string[];
  errorMessage?: string;
}

export interface ProviderFormData {
  name: string;
  type: string;
  baseUrl: string;
  apiKey: string;
  apiVersion?: string;
  secretKey?: string;
  secretId?: string;
  safetySettings?: string;
  customModels?: string;
}
