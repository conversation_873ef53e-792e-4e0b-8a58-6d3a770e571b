.provider-manager {
  .provider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 10px;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
  }
}

.provider-form {
  .form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding: 20px 0 0;
    border-top: 1px solid var(--border-color);
  }
}

.provider-title {
  display: flex;
  align-items: center;
  gap: 8px;

  .default-badge {
    background: var(--primary);
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
  }

  .disabled-badge {
    background: var(--gray);
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
  }
}

.provider-models {
  font-size: 12px;
  color: burlywood;
  margin-top: 4px;
  word-break: break-all;
}

.provider-actions {
  display: flex;
  align-items: center;
  gap: 8px;

  input[type="checkbox"] {
    margin-right: 4px;
  }
}

@media (max-width: 768px) {
  .provider-manager {
    .provider-header {
      flex-direction: column;
      gap: 10px;
      align-items: stretch;

      h3 {
        text-align: center;
      }
    }
  }

  .provider-actions {
    flex-wrap: wrap;
    gap: 4px;
  }

  .form-actions {
    flex-direction: column;
    gap: 8px;
  }
}
