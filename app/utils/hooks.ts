import { useMemo } from "react";
import { useAccessStore, useAppConfig } from "../store";
import { collectModelsWithDefaultModel } from "./model";
import { LLMModel } from "../client/api";
import { ProviderConfig } from "../constant";

// 从自定义提供商生成模型列表
function generateModelsFromProviders(providers: ProviderConfig[]): LLMModel[] {
  const models: LLMModel[] = [];
  let seq = -2000; // 确保自定义提供商的模型排在前面

  providers.forEach((provider) => {
    if (!provider.enabled) return;

    // 优先使用自定义模型列表，如果没有则使用常见模型
    let modelNames: string[] = [];

    if (provider.customModels && provider.customModels.trim()) {
      // 解析自定义模型列表
      modelNames = provider.customModels
        .split(",")
        .map((name) => name.trim())
        .filter((name) => name.length > 0);
    } else {
      // 使用默认的常见模型
      modelNames = getCommonModelsForProvider(provider.type);
    }

    modelNames.forEach((modelName) => {
      models.push({
        name: modelName,
        displayName: `${modelName} (${provider.name})`,
        available: true,
        sorted: seq++,
        provider: {
          id: provider.id,
          providerName: provider.name,
          providerType: provider.type,
          sorted: seq,
        },
      });
    });
  });

  return models;
}

// 根据提供商类型获取常见模型
function getCommonModelsForProvider(providerType: string): string[] {
  const modelMap: Record<string, string[]> = {
    OpenAI: ["gpt-4o", "gpt-4o-mini", "gpt-4", "gpt-3.5-turbo"],
    Azure: ["gpt-4", "gpt-35-turbo"],
    Google: ["gemini-pro", "gemini-pro-vision"],
    Anthropic: ["claude-3-5-sonnet-20241022", "claude-3-haiku-20240307"],
    Baidu: ["ernie-4.0-8k", "ernie-3.5-8k"],
    ByteDance: ["doubao-pro-4k", "doubao-lite-4k"],
    Alibaba: ["qwen-turbo", "qwen-plus"],
    Tencent: ["hunyuan-lite", "hunyuan-standard"],
    Moonshot: ["moonshot-v1-8k", "moonshot-v1-32k"],
    XAI: ["grok-beta"],
    ChatGLM: ["glm-4", "glm-3-turbo"],
    Iflytek: ["spark-lite", "spark-pro"],
    Stability: ["stable-diffusion-xl"],
  };

  return modelMap[providerType] || ["default-model"];
}

export function useAllModels() {
  const accessStore = useAccessStore();
  const configStore = useAppConfig();

  const models = useMemo(() => {
    // 获取原有的模型
    const originalModels = collectModelsWithDefaultModel(
      configStore.models,
      [configStore.customModels, accessStore.customModels].join(","),
      accessStore.defaultModel,
    );

    // 获取来自自定义提供商的模型
    const customProviderModels = generateModelsFromProviders(
      accessStore.multiProviderConfig.providers,
    );

    // 合并模型列表
    return [...customProviderModels, ...originalModels];
  }, [
    accessStore.customModels,
    accessStore.defaultModel,
    accessStore.multiProviderConfig.providers,
    configStore.customModels,
    configStore.models,
  ]);

  return models;
}
