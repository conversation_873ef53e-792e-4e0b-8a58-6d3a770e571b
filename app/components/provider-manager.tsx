import React, { useState } from "react";
import {
  ServiceProvider,
  ProviderConfig,
  GoogleSafetySettingsThreshold,
} from "../constant";
import { ProviderFormData } from "../typing";
import { useAccessStore } from "../store/access";
import { IconButton } from "./button";
import { List, ListItem, Modal, Select } from "./ui-lib";
import { PasswordInput } from "./ui-lib";

import AddIcon from "../icons/add.svg";
import EditIcon from "../icons/edit.svg";
import DeleteIcon from "../icons/delete.svg";
import CheckIcon from "../icons/confirm.svg";
import CancelIcon from "../icons/cancel.svg";

import styles from "./provider-manager.module.scss";

interface ProviderFormProps {
  provider?: ProviderConfig;
  onSave: (provider: ProviderFormData) => void;
  onCancel: () => void;
}

function ProviderForm({ provider, onSave, onCancel }: ProviderFormProps) {
  const [formData, setFormData] = useState<ProviderFormData>({
    name: provider?.name || "",
    type: provider?.type || ServiceProvider.OpenAI,
    baseUrl: provider?.baseUrl || "",
    apiKey: provider?.apiKey || "",
    apiVersion: provider?.apiVersion || "",
    secretKey: provider?.secretKey || "",
    secretId: provider?.secretId || "",
    safetySettings:
      provider?.safetySettings || GoogleSafetySettingsThreshold.BLOCK_ONLY_HIGH,
    customModels: provider?.customModels || "",
  });

  const handleSubmit = () => {
    if (
      !formData.name.trim() ||
      !formData.baseUrl.trim() ||
      !formData.apiKey.trim()
    ) {
      alert("请填写必填字段");
      return;
    }
    onSave(formData);
  };

  const getDefaultUrl = (type: ServiceProvider) => {
    const urlMap = {
      [ServiceProvider.OpenAI]: "https://api.openai.com",
      [ServiceProvider.Azure]: "https://{resource-url}/openai",
      [ServiceProvider.Google]: "https://generativelanguage.googleapis.com/",
      [ServiceProvider.Anthropic]: "https://api.anthropic.com",
      [ServiceProvider.Baidu]: "https://aip.baidubce.com",
      [ServiceProvider.ByteDance]: "https://ark.cn-beijing.volces.com",
      [ServiceProvider.Alibaba]: "https://dashscope.aliyuncs.com/api/",
      [ServiceProvider.Tencent]: "https://hunyuan.tencentcloudapi.com",
      [ServiceProvider.Moonshot]: "https://api.moonshot.cn",
      [ServiceProvider.Stability]: "https://api.stability.ai",
      [ServiceProvider.Iflytek]: "https://spark-api-open.xf-yun.com",
      [ServiceProvider.XAI]: "https://api.x.ai",
      [ServiceProvider.ChatGLM]: "https://open.bigmodel.cn",
    };
    return urlMap[type] || "";
  };

  const handleTypeChange = (type: ServiceProvider) => {
    setFormData((prev) => ({
      ...prev,
      type,
      baseUrl: prev.baseUrl || getDefaultUrl(type),
    }));
  };

  const needsApiVersion = [
    ServiceProvider.Azure,
    ServiceProvider.Google,
  ].includes(formData.type as ServiceProvider);
  const needsSecretKey = [ServiceProvider.Baidu].includes(
    formData.type as ServiceProvider,
  );
  const needsSecretId = [ServiceProvider.Tencent].includes(
    formData.type as ServiceProvider,
  );
  const needsSafetySettings = [ServiceProvider.Google].includes(
    formData.type as ServiceProvider,
  );

  return (
    <div className={styles["provider-form"]}>
      <List>
        <ListItem title="提供商名称" subTitle="自定义提供商名称">
          <input
            type="text"
            value={formData.name}
            placeholder="例如：我的OpenAI"
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                name: (e.target as HTMLInputElement).value,
              }))
            }
          />
        </ListItem>

        <ListItem title="提供商类型" subTitle="选择提供商类型">
          <Select
            value={formData.type}
            onChange={(e) =>
              handleTypeChange(e.target.value as ServiceProvider)
            }
          >
            {Object.entries(ServiceProvider).map(([key, value]) => (
              <option key={key} value={value}>
                {key}
              </option>
            ))}
          </Select>
        </ListItem>

        <ListItem title="API 地址" subTitle="提供商的API基础地址">
          <input
            type="text"
            value={formData.baseUrl}
            placeholder={getDefaultUrl(formData.type as ServiceProvider)}
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                baseUrl: (e.target as HTMLInputElement).value,
              }))
            }
          />
        </ListItem>

        <ListItem title="API 密钥" subTitle="提供商的API密钥">
          <PasswordInput
            value={formData.apiKey}
            type="text"
            placeholder="输入API密钥"
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                apiKey: (e.target as HTMLInputElement).value,
              }))
            }
          />
        </ListItem>

        {needsApiVersion && (
          <ListItem title="API 版本" subTitle="API版本号">
            <input
              type="text"
              value={formData.apiVersion}
              placeholder="例如：2023-08-01-preview"
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, apiVersion: e.target.value }))
              }
            />
          </ListItem>
        )}

        {needsSecretKey && (
          <ListItem title="Secret Key" subTitle="密钥">
            <PasswordInput
              value={formData.secretKey || ""}
              type="text"
              placeholder="输入Secret Key"
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  secretKey: (e.target as HTMLInputElement).value,
                }))
              }
            />
          </ListItem>
        )}

        {needsSecretId && (
          <>
            <ListItem title="Secret ID" subTitle="密钥ID">
              <PasswordInput
                value={formData.secretId || ""}
                type="text"
                placeholder="输入Secret ID"
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    secretId: (e.target as HTMLInputElement).value,
                  }))
                }
              />
            </ListItem>
            <ListItem title="Secret Key" subTitle="密钥">
              <PasswordInput
                value={formData.secretKey || ""}
                type="text"
                placeholder="输入Secret Key"
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    secretKey: (e.target as HTMLInputElement).value,
                  }))
                }
              />
            </ListItem>
          </>
        )}

        {needsSafetySettings && (
          <ListItem title="安全设置" subTitle="Google安全设置级别">
            <Select
              value={formData.safetySettings}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  safetySettings: e.target.value,
                }))
              }
            >
              {Object.entries(GoogleSafetySettingsThreshold).map(
                ([key, value]) => (
                  <option key={key} value={value}>
                    {key}
                  </option>
                ),
              )}
            </Select>
          </ListItem>
        )}

        <ListItem
          title="自定义模型"
          subTitle="自定义模型列表，用逗号分隔，例如：gpt-4,gpt-3.5-turbo"
        >
          <input
            type="text"
            value={formData.customModels || ""}
            placeholder="例如：gpt-4,gpt-3.5-turbo,claude-3"
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                customModels: (e.target as HTMLInputElement).value,
              }))
            }
          />
        </ListItem>
      </List>

      <div className={styles["form-actions"]}>
        <IconButton
          icon={<CheckIcon />}
          text="保存"
          type="primary"
          onClick={handleSubmit}
        />
        <IconButton icon={<CancelIcon />} text="取消" onClick={onCancel} />
      </div>
    </div>
  );
}

interface ProviderItemProps {
  provider: ProviderConfig;
  onEdit: () => void;
  onDelete: () => void;
  onToggleDefault: () => void;
  onToggleEnabled: () => void;
}

function ProviderItem({
  provider,
  onEdit,
  onDelete,
  onToggleDefault,
  onToggleEnabled,
}: ProviderItemProps) {
  return (
    <ListItem
      title={
        <div className={styles["provider-title"]}>
          <span>{provider.name}</span>
          {provider.isDefault && (
            <span className={styles["default-badge"]}>默认</span>
          )}
          {!provider.enabled && (
            <span className={styles["disabled-badge"]}>已禁用</span>
          )}
        </div>
      }
      subTitle={
        <div>
          <div>{`${provider.type} - ${provider.baseUrl}`}</div>
          {provider.customModels && (
            <div className={styles["provider-models"]}>
              模型: {provider.customModels}
            </div>
          )}
        </div>
      }
    >
      <div className={styles["provider-actions"]}>
        <input
          type="checkbox"
          checked={provider.enabled}
          onChange={onToggleEnabled}
          title="启用/禁用"
        />
        <IconButton icon={<EditIcon />} onClick={onEdit} title="编辑" />
        <IconButton icon={<DeleteIcon />} onClick={onDelete} title="删除" />
        {!provider.isDefault && provider.enabled && (
          <IconButton
            text="设为默认"
            onClick={onToggleDefault}
            title="设为默认提供商"
          />
        )}
      </div>
    </ListItem>
  );
}

export function ProviderManager() {
  const accessStore = useAccessStore();
  const [showForm, setShowForm] = useState(false);
  const [editingProvider, setEditingProvider] = useState<
    ProviderConfig | undefined
  >();

  const providers = accessStore.multiProviderConfig.providers;

  const handleAddProvider = () => {
    setEditingProvider(undefined);
    setShowForm(true);
  };

  const handleEditProvider = (provider: ProviderConfig) => {
    setEditingProvider(provider);
    setShowForm(true);
  };

  const handleSaveProvider = (formData: ProviderFormData) => {
    if (editingProvider) {
      accessStore.updateProvider(editingProvider.id, {
        name: formData.name,
        type: formData.type as ServiceProvider,
        baseUrl: formData.baseUrl,
        apiKey: formData.apiKey,
        apiVersion: formData.apiVersion,
        secretKey: formData.secretKey,
        secretId: formData.secretId,
        safetySettings:
          formData.safetySettings as GoogleSafetySettingsThreshold,
        customModels: formData.customModels,
      });
    } else {
      accessStore.addProvider({
        name: formData.name,
        type: formData.type as ServiceProvider,
        baseUrl: formData.baseUrl,
        apiKey: formData.apiKey,
        apiVersion: formData.apiVersion,
        secretKey: formData.secretKey,
        secretId: formData.secretId,
        safetySettings:
          formData.safetySettings as GoogleSafetySettingsThreshold,
        customModels: formData.customModels,
        enabled: true,
        isDefault: providers.length === 0, // 第一个提供商自动设为默认
      });
    }
    setShowForm(false);
  };

  const handleDeleteProvider = (provider: ProviderConfig) => {
    if (confirm(`确定要删除提供商 "${provider.name}" 吗？`)) {
      accessStore.removeProvider(provider.id);
    }
  };

  const handleToggleEnabled = (provider: ProviderConfig) => {
    accessStore.updateProvider(provider.id, { enabled: !provider.enabled });
  };

  const handleSetDefault = (provider: ProviderConfig) => {
    accessStore.setDefaultProvider(provider.id);
  };

  return (
    <div className={styles["provider-manager"]}>
      <div className={styles["provider-header"]}>
        <h3>模型提供商管理</h3>
        <IconButton
          icon={<AddIcon />}
          text="添加提供商"
          type="primary"
          onClick={handleAddProvider}
        />
      </div>

      <List>
        {providers.length === 0 ? (
          <ListItem title="暂无提供商" subTitle="点击上方按钮添加第一个提供商">
            <div />
          </ListItem>
        ) : (
          providers.map((provider) => (
            <ProviderItem
              key={provider.id}
              provider={provider}
              onEdit={() => handleEditProvider(provider)}
              onDelete={() => handleDeleteProvider(provider)}
              onToggleDefault={() => handleSetDefault(provider)}
              onToggleEnabled={() => handleToggleEnabled(provider)}
            />
          ))
        )}
      </List>

      {showForm && (
        <Modal
          title={editingProvider ? "编辑提供商" : "添加提供商"}
          onClose={() => setShowForm(false)}
        >
          <ProviderForm
            provider={editingProvider}
            onSave={handleSaveProvider}
            onCancel={() => setShowForm(false)}
          />
        </Modal>
      )}
    </div>
  );
}
