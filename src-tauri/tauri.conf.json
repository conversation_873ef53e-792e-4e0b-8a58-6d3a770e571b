{"$schema": "../node_modules/@tauri-apps/cli/schema.json", "build": {"beforeBuildCommand": "yarn export", "beforeDevCommand": "yarn export:dev", "devPath": "http://localhost:3000", "distDir": "../out", "withGlobalTauri": true}, "package": {"productName": "NextChat", "version": "2.15.7"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "dialog": {"all": true, "ask": true, "confirm": true, "message": true, "open": true, "save": true}, "clipboard": {"all": true, "writeText": true, "readText": true}, "window": {"all": false, "close": true, "hide": true, "maximize": true, "minimize": true, "setIcon": true, "setIgnoreCursorEvents": true, "setResizable": true, "show": true, "startDragging": true, "unmaximize": true, "unminimize": true}, "fs": {"all": true}, "notification": {"all": true}, "http": {"all": true, "request": true, "scope": ["https://*", "http://*"]}}, "bundle": {"active": true, "category": "DeveloperTool", "copyright": "2023, <PERSON>i All Rights Reserved.", "deb": {"depends": []}, "externalBin": [], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "identifier": "com.yida.chatgpt.next.web", "longDescription": "NextChat is a cross-platform ChatGPT client, including Web/Win/Linux/OSX/PWA.", "macOS": {"entitlements": null, "exceptionDomain": "", "frameworks": [], "providerShortName": null, "signingIdentity": null}, "resources": [], "shortDescription": "NextChat App", "targets": "all", "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}}, "security": {"csp": null, "dangerousUseHttpScheme": true}, "updater": {"active": false, "endpoints": ["https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web/releases/latest/download/latest.json"], "dialog": true, "windows": {"installMode": "passive"}, "pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IERFNDE4MENFM0Y1RTZBOTQKUldTVWFsNC96b0JCM3RqM2NmMnlFTmxIaStRaEJrTHNOU2VqRVlIV1hwVURoWUdVdEc1eDcxVEYK"}, "windows": [{"fullscreen": false, "height": 600, "resizable": true, "title": "NextChat", "width": 960, "hiddenTitle": true, "titleBarStyle": "Overlay"}]}}